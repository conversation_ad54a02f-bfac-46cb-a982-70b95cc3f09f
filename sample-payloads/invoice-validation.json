{"id": "INV-2024-001", "uuid": "550e8400-e29b-41d4-a716-************", "issueDate": "2024-06-16", "issueTime": "10:30:00", "profileID": "reporting:1.0", "documentCurrencyCode": "SAR", "taxCurrencyCode": "SAR", "invoiceTypeCode": "388", "accountingSupplierParty": {"companyID": "***************", "partyName": "Test Company Ltd", "postalAddress": {"streetName": "King Fahd Road", "buildingNumber": "1234", "cityName": "Riyadh", "postalZone": "12345", "countrySubentity": "Riyadh Province", "country": "SA"}, "partyTaxScheme": {"companyID": "***************", "taxScheme": {"id": "VAT"}}}, "accountingCustomerParty": {"companyID": "***************", "partyName": "Customer Company Ltd", "postalAddress": {"streetName": "Prince <PERSON>", "buildingNumber": "5678", "cityName": "Jeddah", "postalZone": "54321", "countrySubentity": "Makkah Province", "country": "SA"}}, "taxTotal": {"taxAmount": 15.0, "taxSubtotal": [{"taxableAmount": 100.0, "taxAmount": 15.0, "taxCategory": {"id": "S", "percent": 15.0, "taxScheme": {"id": "VAT"}}}]}, "legalMonetaryTotal": {"lineExtensionAmount": 100.0, "taxExclusiveAmount": 100.0, "taxInclusiveAmount": 115.0, "payableAmount": 115.0, "prepaidAmount": 0.0}, "invoiceLines": [{"id": "1", "invoicedQuantity": 1, "lineExtensionAmount": 100.0, "item": {"name": "Test Product", "classifiedTaxCategory": {"id": "S", "percent": 15.0, "taxScheme": {"id": "VAT"}}}, "price": {"priceAmount": 100.0}, "taxPercentage": 15.0}], "additionalDocumentReference": [{"id": "ICV", "uuid": "1"}, {"id": "PIH", "attachment": {"embeddedDocumentBinaryObject": "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ=="}}]}