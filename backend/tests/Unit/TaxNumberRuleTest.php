<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Rules\TaxNumberRule;
use App\Models\BusinessLocation;
use App\Models\Company;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TaxNumberRuleTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test company
        $this->company = Company::create([
            'name' => 'Test Company',
        ]);
    }

    /** @test */
    public function it_allows_null_tax_number()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        
        $rule->validate('tax_no', null, function ($message) use (&$passed) {
            $passed = false;
        });
        
        $this->assertTrue($passed);
    }

    /** @test */
    public function it_allows_empty_string_tax_number()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        
        $rule->validate('tax_no', '', function ($message) use (&$passed) {
            $passed = false;
        });
        
        $this->assertTrue($passed);
    }

    /** @test */
    public function it_rejects_non_string_tax_number()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        $errorMessage = '';
        
        $rule->validate('tax_no', 123456789012345, function ($message) use (&$passed, &$errorMessage) {
            $passed = false;
            $errorMessage = $message;
        });
        
        $this->assertFalse($passed);
        $this->assertStringContains('must be a string', $errorMessage);
    }

    /** @test */
    public function it_rejects_tax_number_with_less_than_15_digits()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        $errorMessage = '';
        
        $rule->validate('tax_no', '12345678901234', function ($message) use (&$passed, &$errorMessage) {
            $passed = false;
            $errorMessage = $message;
        });
        
        $this->assertFalse($passed);
        $this->assertStringContains('must be exactly 15 digits', $errorMessage);
    }

    /** @test */
    public function it_rejects_tax_number_with_more_than_15_digits()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        $errorMessage = '';
        
        $rule->validate('tax_no', '1234567890123456', function ($message) use (&$passed, &$errorMessage) {
            $passed = false;
            $errorMessage = $message;
        });
        
        $this->assertFalse($passed);
        $this->assertStringContains('must be exactly 15 digits', $errorMessage);
    }

    /** @test */
    public function it_rejects_tax_number_with_non_numeric_characters()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        $errorMessage = '';
        
        $rule->validate('tax_no', '12345678901234a', function ($message) use (&$passed, &$errorMessage) {
            $passed = false;
            $errorMessage = $message;
        });
        
        $this->assertFalse($passed);
        $this->assertStringContains('must be exactly 15 digits', $errorMessage);
    }

    /** @test */
    public function it_accepts_valid_15_digit_tax_number()
    {
        $rule = new TaxNumberRule();
        $passed = true;
        
        $rule->validate('tax_no', '123456789012345', function ($message) use (&$passed) {
            $passed = false;
        });
        
        $this->assertTrue($passed);
    }

    /** @test */
    public function it_rejects_duplicate_tax_number()
    {
        // Create a business location with a tax number
        BusinessLocation::create([
            'company_id' => $this->company->id,
            'tax_no' => '123456789012345',
        ]);
        
        $rule = new TaxNumberRule();
        $passed = true;
        $errorMessage = '';
        
        $rule->validate('tax_no', '123456789012345', function ($message) use (&$passed, &$errorMessage) {
            $passed = false;
            $errorMessage = $message;
        });
        
        $this->assertFalse($passed);
        $this->assertStringContains('has already been taken', $errorMessage);
    }

    /** @test */
    public function it_allows_same_tax_number_when_excluding_current_record()
    {
        // Create a business location with a tax number
        $location = BusinessLocation::create([
            'company_id' => $this->company->id,
            'tax_no' => '123456789012345',
        ]);
        
        $rule = new TaxNumberRule($location->id);
        $passed = true;
        
        $rule->validate('tax_no', '123456789012345', function ($message) use (&$passed) {
            $passed = false;
        });
        
        $this->assertTrue($passed);
    }
}
