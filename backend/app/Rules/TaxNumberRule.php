<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\BusinessLocation;

/**
 * Tax Number Validation Rule
 * 
 * Validates tax numbers for business locations according to requirements:
 * - Must be exactly 15 digits
 * - Must be numeric only
 * - Must be unique across all business locations
 */
class TaxNumberRule implements ValidationRule
{
    private ?int $excludeId;
    
    /**
     * Create a new rule instance.
     * 
     * @param int|null $excludeId ID to exclude from uniqueness check (for updates)
     */
    public function __construct(?int $excludeId = null)
    {
        $this->excludeId = $excludeId;
    }
    
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Allow null/empty values since tax_no is nullable
        if (is_null($value) || $value === '') {
            return;
        }
        
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }
        
        // Check if tax number is exactly 15 digits
        if (!preg_match('/^\d{15}$/', $value)) {
            $fail('The :attribute must be exactly 15 digits.');
            return;
        }
        
        // Check uniqueness across business locations
        if (!$this->isUnique($value)) {
            $fail('The :attribute has already been taken.');
            return;
        }
    }
    
    /**
     * Check if the tax number is unique
     */
    private function isUnique(string $taxNumber): bool
    {
        $query = BusinessLocation::where('tax_no', $taxNumber);
        
        // Exclude current record if updating
        if ($this->excludeId) {
            $query->where('id', '!=', $this->excludeId);
        }
        
        return !$query->exists();
    }
}
