<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Rules\TaxNumberRule;

/**
 * Simple test script to demonstrate tax number validation
 * Run with: php test_tax_validation.php
 */

echo "=== Tax Number Validation Test ===\n\n";

$testCases = [
    // Valid cases
    ['123456789012345', 'Valid 15-digit number'],
    [null, 'Null value (allowed)'],
    ['', 'Empty string (allowed)'],
    
    // Invalid cases with letters
    ['12345678901234a', 'Contains lowercase letter'],
    ['1234567890123A5', 'Contains uppercase letter'],
    ['12345ABC7890123', 'Contains multiple letters'],
    
    // Invalid cases with symbols/spaces
    ['123456789012-45', 'Contains hyphen'],
    ['123456789012 45', 'Contains space'],
    ['123456789012.45', 'Contains period'],
    ['123456789012#45', 'Contains hash symbol'],
    
    // Invalid length cases
    ['1234567890123', 'Too short (13 digits)'],
    ['12345678901234', 'Too short (14 digits)'],
    ['1234567890123456', 'Too long (16 digits)'],
    ['12345678901234567', 'Too long (17 digits)'],
    
    // Edge cases
    [123456789012345, 'Integer instead of string'],
    ['000000000000000', 'All zeros (valid format)'],
    ['999999999999999', 'All nines (valid format)'],
];

foreach ($testCases as [$value, $description]) {
    echo "Testing: $description\n";
    echo "Value: " . (is_null($value) ? 'NULL' : (is_string($value) ? "'$value'" : $value)) . "\n";
    
    $rule = new TaxNumberRule();
    $passed = true;
    $errorMessage = '';
    
    $rule->validate('tax_no', $value, function ($message) use (&$passed, &$errorMessage) {
        $passed = false;
        $errorMessage = $message;
    });
    
    if ($passed) {
        echo "Result: ✅ PASSED\n";
    } else {
        echo "Result: ❌ FAILED\n";
        echo "Error: $errorMessage\n";
    }
    
    echo str_repeat('-', 50) . "\n";
}

echo "\n=== Summary ===\n";
echo "The TaxNumberRule now provides specific error messages for:\n";
echo "1. Letters in tax number\n";
echo "2. Symbols and special characters\n";
echo "3. Incorrect length with current digit count\n";
echo "4. Type validation\n";
echo "5. Uniqueness validation\n";
